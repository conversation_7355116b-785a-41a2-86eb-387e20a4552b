<?php

namespace App\Services;

use App\Repositories\Contracts\Form01AReportRepositoryInterface;
use Illuminate\Pagination\LengthAwarePaginator;

class Form01AReportService
{
    protected $repository;

    public function __construct(Form01AReportRepositoryInterface $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Generate Form 01A report with pagination
     * 
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function generateReport(array $filters = [], int $perPage = 50): LengthAwarePaginator
    {
        // Filter out infrastructure types if specified
        $filters = $this->processInfrastructureTypeFilters($filters);
        
        return $this->repository->generateReport($filters, $perPage);
    }

    /**
     * Generate Form 01A summary statistics
     * 
     * @param array $filters
     * @return array
     */
    public function generateSummary(array $filters = []): array
    {
        // Filter out infrastructure types if specified
        $filters = $this->processInfrastructureTypeFilters($filters);
        
        $summary = $this->repository->generateSummary($filters);
        
        // Add user information if provided
        if (!empty($filters['user_name']) || !empty($filters['user_phone']) || !empty($filters['user_email'])) {
            $summary['user_info'] = [
                'name' => $filters['user_name'] ?? null,
                'phone' => $filters['user_phone'] ?? null,
                'email' => $filters['user_email'] ?? null,
            ];
        }

        // Add filter information for reference
        $summary['applied_filters'] = $this->getAppliedFiltersInfo($filters);
        
        return $summary;
    }

    /**
     * Process infrastructure type filters
     * 
     * @param array $filters
     * @return array
     */
    protected function processInfrastructureTypeFilters(array $filters): array
    {
        // If specific infrastructure types are requested, we'll handle this in the repository
        // by modifying the union query to only include selected types
        if (!empty($filters['infrastructure_types']) && is_array($filters['infrastructure_types'])) {
            $validTypes = ['trambom', 'congdap', 'kenhmuong'];
            $filters['infrastructure_types'] = array_intersect($filters['infrastructure_types'], $validTypes);
        }

        return $filters;
    }

    /**
     * Get applied filters information for summary
     * 
     * @param array $filters
     * @return array
     */
    protected function getAppliedFiltersInfo(array $filters): array
    {
        $appliedFilters = [];

        if (!empty($filters['ten_doituong'])) {
            $appliedFilters['ten_doituong'] = $filters['ten_doituong'];
        }

        if (!empty($filters['ma_donvi'])) {
            $appliedFilters['ma_donvi'] = $filters['ma_donvi'];
        }

        if (!empty($filters['xa_phuong'])) {
            $appliedFilters['xa_phuong'] = $filters['xa_phuong'];
        }

        if (!empty($filters['quan_huyen'])) {
            $appliedFilters['quan_huyen'] = $filters['quan_huyen'];
        }

        if (!empty($filters['tinh_thanh'])) {
            $appliedFilters['tinh_thanh'] = $filters['tinh_thanh'];
        }

        if (!empty($filters['infrastructure_types'])) {
            $appliedFilters['infrastructure_types'] = $filters['infrastructure_types'];
        }

        if (!empty($filters['nam_tu']) || !empty($filters['nam_den'])) {
            $appliedFilters['year_range'] = [
                'from' => $filters['nam_tu'] ?? null,
                'to' => $filters['nam_den'] ?? null,
            ];
        }

        if (!empty($filters['search'])) {
            $appliedFilters['search'] = $filters['search'];
        }

        return $appliedFilters;
    }

    /**
     * Export report data to array format suitable for document generation
     * 
     * @param array $filters
     * @return array
     */
    public function exportReportData(array $filters = []): array
    {
        $filters = $this->processInfrastructureTypeFilters($filters);
        
        // Get all data without pagination for export
        $allData = $this->repository->getInfrastructureData($filters)->get();
        $summary = $this->repository->generateSummary($filters);

        return [
            'report_data' => $allData->toArray(),
            'summary' => $summary,
            'export_info' => [
                'generated_at' => now()->toISOString(),
                'total_records' => $allData->count(),
                'filters_applied' => $this->getAppliedFiltersInfo($filters),
            ],
        ];
    }
}
