<?php

namespace App\Repositories;

use App\Repositories\Contracts\Form01AReportRepositoryInterface;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class Form01AReportRepository implements Form01AReportRepositoryInterface
{
    protected $cachePrefix = 'form01a_report_';
    protected $cacheTTL = 1800; // 30 minutes

    /**
     * Generate paginated Form 01A report data
     */
    public function generateReport(array $filters = [], int $perPage = 50): LengthAwarePaginator
    {
        $cacheKey = $this->cachePrefix . 'data_' . md5(json_encode($filters) . $perPage);

        return Cache::remember($cacheKey, $this->cacheTTL, function () use ($filters, $perPage) {
            $query = $this->getInfrastructureData($filters);

            return $query->paginate($perPage);
        });
    }

    /**
     * Generate Form 01A summary statistics
     */
    public function generateSummary(array $filters = []): array
    {
        $cacheKey = $this->cachePrefix . 'summary_' . md5(json_encode($filters));

        return Cache::remember($cacheKey, $this->cacheTTL, function () use ($filters) {
            $query = $this->getInfrastructureData($filters);

            $summary = $query->selectRaw('
                COUNT(*) as total_quantity,
                SUM(CASE WHEN loai_ct_full = \'Trạm bơm\' THEN 1 ELSE 0 END) as trambom_count,
                SUM(CASE WHEN loai_ct_full = \'Cống đập\' THEN 1 ELSE 0 END) as congdap_count,
                SUM(CASE WHEN loai_ct_full = \'Kênh mương\' THEN 1 ELSE 0 END) as kenhmuong_count,
                SUM(COALESCE(nguyengia, 0)) as total_original_cost,
                SUM(COALESCE(gtcl, 0)) as total_remaining_value,
                SUM(COALESCE(khau_hao, 0)) as total_depreciation,
                AVG(COALESCE(dt_dat, 0)) as avg_land_area
            ')->first();

            return [
                'total_quantity' => (int) $summary->total_quantity,
                'infrastructure_breakdown' => [
                    'trambom' => (int) $summary->trambom_count,
                    'congdap' => (int) $summary->congdap_count,
                    'kenhmuong' => (int) $summary->kenhmuong_count,
                ],
                'financial_summary' => [
                    'total_original_cost' => (float) $summary->total_original_cost,
                    'total_remaining_value' => (float) $summary->total_remaining_value,
                    'total_depreciation' => (float) $summary->total_depreciation,
                ],
                'avg_land_area' => (float) $summary->avg_land_area,
                'generated_at' => now()->toISOString(),
            ];
        });
    }

    /**
     * Get infrastructure data from all tables
     */
    public function getInfrastructureData(array $filters = [])
    {
        $currentYear = date('Y');

        // Build UNION query for all three infrastructure types
        $trambomQuery = DB::connection('pgsql')
            ->table('taisan.trambom as t')
            ->leftJoin('taisan.quyettoan as qt', 't.id_qt', '=', 'qt.id')
            ->leftJoin('basemap.rg_xa as xa', 't.id_xa', '=', 'xa.id')
            ->selectRaw("
                t.id,
                t.ten,
                t.quymo_ct,
                1 as so_luong,
                'Trạm bơm' as loai_ct_full,
                t.nam_sd,
                t.dt_dat,
                COALESCE(qt.nguyengia, 0) as nguyengia,
                CASE
                    WHEN qt.nguyengia IS NOT NULL AND t.nam_sd IS NOT NULL
                    THEN ({$currentYear} - t.nam_sd) * 0.04 * qt.nguyengia
                    ELSE 0
                END as khau_hao,
                CASE
                    WHEN qt.nguyengia IS NOT NULL AND t.nam_sd IS NOT NULL
                    THEN qt.nguyengia - (({$currentYear} - t.nam_sd) * 0.04 * qt.nguyengia)
                    ELSE COALESCE(qt.nguyengia, 0)
                END as gtcl,
                COALESCE(t.tinhtrang, 'Đang hoạt động') as tinhtrang,
                t.chuthich,
                xa.tenxa,
                xa.tenhuyen,
                t.dv_quanly,
                'trambom' as source_table
            ");

        $congdapQuery = DB::connection('pgsql')
            ->table('taisan.congdap as c')
            ->leftJoin('taisan.quyettoan as qt', 'c.id_qt', '=', 'qt.id')
            ->leftJoin('basemap.rg_xa as xa', 'c.id_xa', '=', 'xa.id')
            ->selectRaw("
                c.id,
                c.ten,
                c.quymo_ct,
                1 as so_luong,
                'Cống đập' as loai_ct_full,
                c.nam_sd,
                c.dt_dat,
                COALESCE(qt.nguyengia, 0) as nguyengia,
                CASE
                    WHEN qt.nguyengia IS NOT NULL AND c.nam_sd IS NOT NULL
                    THEN ({$currentYear} - c.nam_sd) * 0.04 * qt.nguyengia
                    ELSE 0
                END as khau_hao,
                CASE
                    WHEN qt.nguyengia IS NOT NULL AND c.nam_sd IS NOT NULL
                    THEN qt.nguyengia - (({$currentYear} - c.nam_sd) * 0.04 * qt.nguyengia)
                    ELSE COALESCE(qt.nguyengia, 0)
                END as gtcl,
                COALESCE(c.tinhtrang, 'Đang hoạt động') as tinhtrang,
                c.chuthich,
                xa.tenxa,
                xa.tenhuyen,
                c.dv_quanly,
                'congdap' as source_table
            ");

        $kenhmuongQuery = DB::connection('pgsql')
            ->table('taisan.kenhmuong as k')
            ->leftJoin('taisan.quyettoan as qt', 'k.id_qt', '=', 'qt.id')
            ->leftJoin('basemap.rg_xa as xa', 'k.id_xa', '=', 'xa.id')
            ->selectRaw("
                k.id,
                k.ten,
                k.quymo_ct,
                1 as so_luong,
                'Kênh mương' as loai_ct_full,
                k.nam_sd,
                k.dt_dat,
                COALESCE(qt.nguyengia, 0) as nguyengia,
                CASE
                    WHEN qt.nguyengia IS NOT NULL AND k.nam_sd IS NOT NULL
                    THEN ({$currentYear} - k.nam_sd) * 0.04 * qt.nguyengia
                    ELSE 0
                END as khau_hao,
                CASE
                    WHEN qt.nguyengia IS NOT NULL AND k.nam_sd IS NOT NULL
                    THEN qt.nguyengia - (({$currentYear} - k.nam_sd) * 0.04 * qt.nguyengia)
                    ELSE COALESCE(qt.nguyengia, 0)
                END as gtcl,
                COALESCE(k.tinhtrang, 'Đang hoạt động') as tinhtrang,
                k.chuthich,
                xa.tenxa,
                xa.tenhuyen,
                k.dv_quanly,
                'kenhmuong' as source_table
            ");

        // Apply filters to each query
        $this->applyFilters($trambomQuery, $filters, 't');
        $this->applyFilters($congdapQuery, $filters, 'c');
        $this->applyFilters($kenhmuongQuery, $filters, 'k');

        // Handle infrastructure type filtering
        $queries = [];
        $allowedTypes = $filters['infrastructure_types'] ?? ['trambom', 'congdap', 'kenhmuong'];

        if (in_array('trambom', $allowedTypes)) {
            $queries[] = $trambomQuery;
        }
        if (in_array('congdap', $allowedTypes)) {
            $queries[] = $congdapQuery;
        }
        if (in_array('kenhmuong', $allowedTypes)) {
            $queries[] = $kenhmuongQuery;
        }

        // If no valid types selected, return empty query
        if (empty($queries)) {
            return DB::connection('pgsql')->table(DB::raw('(SELECT NULL LIMIT 0) as empty_result'));
        }

        // Combine selected queries with UNION
        $unionQuery = array_shift($queries);
        foreach ($queries as $query) {
            $unionQuery = $unionQuery->union($query);
        }

        return DB::query()->fromSub($unionQuery, 'combined_infrastructure')
            ->orderBy('ten');
    }

    /**
     * Apply filters to query
     */
    protected function applyFilters($query, array $filters, string $tableAlias)
    {
        if (!empty($filters['ten_doituong'])) {
            $query->where("{$tableAlias}.ten", 'ilike', '%' . $filters['ten_doituong'] . '%');
        }

        if (!empty($filters['id_xa'])) {
            $query->where("{$tableAlias}.id_xa", $filters['id_xa']);
        }

        if (!empty($filters['loai_ct'])) {
            $query->where("{$tableAlias}.loai_ct", $filters['loai_ct']);
        }

        if (!empty($filters['xa_phuong'])) {
            $query->where('xa.tenxa', 'ilike', '%' . $filters['xa_phuong'] . '%');
        }

        if (!empty($filters['quan_huyen'])) {
            $query->where('xa.tenhuyen', 'ilike', '%' . $filters['quan_huyen'] . '%');
        }

        if (!empty($filters['nam_tu'])) {
            $query->where("{$tableAlias}.nam_sd", '>=', $filters['nam_tu']);
        }

        if (!empty($filters['nam_den'])) {
            $query->where("{$tableAlias}.nam_sd", '<=', $filters['nam_den']);
        }

        if (!empty($filters['search'])) {
            $searchTerm = trim($filters['search']);
            $query->where(function($q) use ($searchTerm, $tableAlias) {
                $q->where("{$tableAlias}.ten", 'ilike', '%' . $searchTerm . '%')
                  ->orWhere("{$tableAlias}.quymo_ct", 'ilike', '%' . $searchTerm . '%')
                  ->orWhere("{$tableAlias}.dv_quanly", 'ilike', '%' . $searchTerm . '%');
            });
        }
    }

    /**
     * Calculate GTCL (remaining value) using depreciation formula
     */
    public function calculateGTCL(int $currentYear, int $yearOfUse, float $originalCost): float
    {
        if ($yearOfUse <= 0 || $originalCost <= 0) {
            return $originalCost;
        }

        $yearsUsed = $currentYear - $yearOfUse;
        $depreciationRate = 0.04; // 4%
        $totalDepreciation = $yearsUsed * $depreciationRate * $originalCost;

        $remainingValue = $originalCost - $totalDepreciation;

        // Ensure remaining value doesn't go below 0
        return max(0, $remainingValue);
    }
}
