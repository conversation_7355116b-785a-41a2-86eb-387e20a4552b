<template>
  <MainLayout>
    <template #sidebar>
      <div class="p-4">
        <h3 class="text-lg font-medium text-gray-700 mb-4">Q<PERSON><PERSON>n lý dữ liệu</h3>
        <div class="flex flex-col gap-2">
          <button class="p-2 border border-gray-200 rounded bg-white hover:bg-gray-50 transition-colors">Thêm dữ liệu</button>
          <button class="p-2 border border-gray-200 rounded bg-white hover:bg-gray-50 transition-colors">Nhập dữ liệu</button>
          <button class="p-2 border border-gray-200 rounded bg-white hover:bg-gray-50 transition-colors">Xuất dữ liệu</button>
        </div>
      </div>
    </template>

    <div class="h-full flex flex-col">
      <div class="flex gap-4 mb-4">
        <input
          type="text"
          v-model="searchText"
          placeholder="Tìm kiếm..."
          class="flex-1 p-2 border border-gray-200 rounded"
          @input="onSearch"
        />
        <select
          v-model="selectedType"
          class="p-2 border border-gray-200 rounded min-w-[150px]"
          @change="onFilterChange"
        >
          <option value="">Tất cả</option>
          <option value="Cống">Cống</option>
          <option value="Đập">Đập</option>
          <option value="Trạm bơm">Trạm bơm</option>
        </select>
        <button
          @click="exportData"
          class="p-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Xuất CSV
        </button>
      </div>

      <div class="flex-1">
        <DataTable
          ref="dataTableRef"
          :columnDefs="columnDefs"
          :rowData="rowData"
          :gridOptions="gridOptions"
          theme="ag-theme-tskchtl"
          height="calc(100vh - 200px)"
          locale="vi-VN"
          @row-clicked="onRowClicked"
          @row-selected="onRowSelected"
        />
      </div>
    </div>
  </MainLayout>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import MainLayout from '@/layouts/MainLayout.vue'
import DataTable from '@/components/DataTable.vue'

// Tham chiếu đến DataTable
const dataTableRef = ref<InstanceType<typeof DataTable> | null>(null)

// Biến cho tìm kiếm và lọc
const searchText = ref('')
const selectedType = ref('')

// Cấu hình cột
const columnDefs = [
  {
    field: 'id',
    headerName: 'ID',
    sortable: true,
    filter: true,
    width: 80,
    checkboxSelection: true,
    headerCheckboxSelection: true
  },
  {
    field: 'ten',
    headerName: 'Tên',
    sortable: true,
    filter: true,
    flex: 1
  },
  {
    field: 'loai',
    headerName: 'Loại',
    sortable: true,
    filter: true,
    width: 120
  },
  {
    field: 'xa',
    headerName: 'Xã/Phường',
    sortable: true,
    filter: true,
    width: 150
  },
  {
    field: 'tinhtrang',
    headerName: 'Tình trạng',
    sortable: true,
    filter: true,
    width: 120
  },
  {
    headerName: 'Thao tác',
    width: 150,
    sortable: false,
    filter: false,
    cellRenderer: (params: any) => {
      const editBtn = document.createElement('button')
      editBtn.innerHTML = 'Sửa'
      editBtn.className = 'px-2 py-1 bg-blue-600 text-white rounded mr-2 hover:bg-blue-700'
      editBtn.addEventListener('click', () => onEdit(params.data))

      const deleteBtn = document.createElement('button')
      deleteBtn.innerHTML = 'Xóa'
      deleteBtn.className = 'px-2 py-1 bg-red-600 text-white rounded hover:bg-red-700'
      deleteBtn.addEventListener('click', () => onDelete(params.data))

      const container = document.createElement('div')
      container.appendChild(editBtn)
      container.appendChild(deleteBtn)

      return container
    }
  }
]

// Dữ liệu mẫu
const rowData = ref([
  { id: 1, ten: 'Cống Rạch Tra', loai: 'Cống', xa: 'Phường 1', tinhtrang: 'Tốt' },
  { id: 2, ten: 'Đập Tân Hòa', loai: 'Đập', xa: 'Phường 2', tinhtrang: 'Đang sửa chữa' },
  { id: 3, ten: 'Cống Bình Điền', loai: 'Cống', xa: 'Phường 3', tinhtrang: 'Tốt' },
  { id: 4, ten: 'Đập Thủ Đức', loai: 'Đập', xa: 'Phường 4', tinhtrang: 'Xuống cấp' },
  { id: 5, ten: 'Cống Nhiêu Lộc', loai: 'Cống', xa: 'Phường 5', tinhtrang: 'Tốt' },
  { id: 6, ten: 'Trạm bơm Bình Thành', loai: 'Trạm bơm', xa: 'Phường 6', tinhtrang: 'Tốt' },
  { id: 7, ten: 'Trạm bơm Tân Hóa', loai: 'Trạm bơm', xa: 'Phường 7', tinhtrang: 'Đang sửa chữa' },
  { id: 8, ten: 'Cống Tân Thuận', loai: 'Cống', xa: 'Phường 8', tinhtrang: 'Xuống cấp' },
  { id: 9, ten: 'Đập Phú Định', loai: 'Đập', xa: 'Phường 9', tinhtrang: 'Tốt' },
  { id: 10, ten: 'Trạm bơm An Phú', loai: 'Trạm bơm', xa: 'Phường 10', tinhtrang: 'Tốt' }
])

// Cấu hình grid
const gridOptions = {
  pagination: true,
  paginationPageSize: 10,
  paginationPageSizeSelector: [5, 10, 20, 50],
  domLayout: 'autoHeight',
  animateRows: true,
  rowSelection: 'multiple',
  suppressRowClickSelection: true
}

// Xử lý tìm kiếm
function onSearch() {
  if (dataTableRef.value) {
    dataTableRef.value.searchData(searchText.value)
  }
}

// Xử lý lọc theo loại
function onFilterChange() {
  if (!dataTableRef.value || !dataTableRef.value.gridApi) return

  const filterInstance = dataTableRef.value.gridApi.getFilterInstance('loai')
  if (filterInstance) {
    if (selectedType.value) {
      filterInstance.setModel({
        type: 'equals',
        filter: selectedType.value
      })
    } else {
      filterInstance.setModel(null)
    }
    dataTableRef.value.gridApi.onFilterChanged()
  }
}

// Xử lý khi click vào hàng
function onRowClicked(event: any) {
  console.log('Đã chọn:', event.data)
}

// Xử lý khi chọn hàng
function onRowSelected(selectedRows: any[]) {
  console.log('Các hàng đã chọn:', selectedRows)
}

// Xử lý khi click vào nút sửa
function onEdit(data: any) {
  console.log('Sửa:', data)
  // Thêm logic xử lý sửa dữ liệu
}

// Xử lý khi click vào nút xóa
function onDelete(data: any) {
  console.log('Xóa:', data)
  // Thêm logic xử lý xóa dữ liệu
}

// Xuất dữ liệu ra CSV
function exportData() {
  if (dataTableRef.value) {
    dataTableRef.value.exportToCSV('du-lieu-tai-san.csv')
  }
}
</script>

<style scoped>
.data-container {
  padding: 1rem;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.data-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-shrink: 0;
}

.search-input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  flex: 1;
}

.filter-select {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 150px;
}

.data-table {
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: auto;
  flex: 1;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #eee;
}

th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.action-btn {
  padding: 0.25rem 0.5rem;
  border: none;
  border-radius: 4px;
  background-color: #007bff;
  color: white;
  cursor: pointer;
  margin-right: 0.5rem;
}

.action-btn.delete {
  background-color: #dc3545;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.control-btn {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

.control-btn:hover {
  background-color: #f8f9fa;
}

.data-controls {
  padding: 1rem;
}

.data-controls h3 {
  margin-bottom: 1rem;
  color: #333;
}
</style>