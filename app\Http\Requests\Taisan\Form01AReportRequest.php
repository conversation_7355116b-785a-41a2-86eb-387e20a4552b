<?php

namespace App\Http\Requests\Taisan;

use Illuminate\Foundation\Http\FormRequest;

class Form01AReportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            // Filtering parameters
            'ten_doituong' => 'nullable|string|max:100',
            'ma_donvi' => 'nullable|string|max:20',
            'thon_xom' => 'nullable|string|max:100',
            'xa_phuong' => 'nullable|string|max:100',
            'quan_huyen' => 'nullable|string|max:100',
            'tinh_thanh' => 'nullable|string|max:100',
            'id_xa' => 'nullable|string|max:5',
            'loai_ct' => 'nullable|string|max:5',
            'infrastructure_types' => 'nullable|array',
            'infrastructure_types.*' => 'in:trambom,congdap,kenhmuong',
            'nam_tu' => 'nullable|integer|min:1900|max:' . date('Y'),
            'nam_den' => 'nullable|integer|min:1900|max:' . date('Y'),
            'search' => 'nullable|string|max:255',
            
            // User information
            'user_name' => 'nullable|string|max:100',
            'user_phone' => 'nullable|string|max:20',
            'user_email' => 'nullable|email|max:100',
            
            // Pagination
            'per_page' => 'nullable|integer|min:1|max:100',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'infrastructure_types.*.in' => 'Loại công trình phải là một trong: trambom, congdap, kenhmuong',
            'nam_tu.min' => 'Năm từ phải lớn hơn hoặc bằng 1900',
            'nam_tu.max' => 'Năm từ không được lớn hơn năm hiện tại',
            'nam_den.min' => 'Năm đến phải lớn hơn hoặc bằng 1900',
            'nam_den.max' => 'Năm đến không được lớn hơn năm hiện tại',
            'per_page.max' => 'Số bản ghi trên trang không được vượt quá 100',
            'user_email.email' => 'Email không đúng định dạng',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Validate that nam_tu <= nam_den if both are provided
            if ($this->filled('nam_tu') && $this->filled('nam_den')) {
                if ($this->input('nam_tu') > $this->input('nam_den')) {
                    $validator->errors()->add('nam_den', 'Năm đến phải lớn hơn hoặc bằng năm từ');
                }
            }
        });
    }
}
