<?php

use App\Http\Controllers\API\Taisan\CongdapController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/* Route::middleware('auth:sanctum')->prefix('taisan')->group(function () { */

Route::prefix('taisan')->group(function () {
   Route::prefix('congdap')->group(function () {
      // Đặt các route cụ thể trước route resource để tránh xung đột
      Route::get('geometry', [CongdapController::class, 'geometry'])->name('congdap.geometry');
      Route::get('attributes', [CongdapController::class, 'attributes'])->name('congdap.attributes');

      // Đặt apiResource sau các route cụ thể
      Route::apiResource('', CongdapController::class);
   });
});
