<?php

use App\Http\Controllers\API\Taisan\CongdapController;
use App\Http\Controllers\API\Taisan\Form01AReportController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/* Route::middleware('auth:sanctum')->prefix('taisan')->group(function () { */

Route::prefix('taisan')->group(function () {
   Route::prefix('congdap')->group(function () {
      // Đặt các route cụ thể trước route resource để tránh xung đột
      Route::get('geometry', [CongdapController::class, 'geometry'])->name('congdap.geometry');
      Route::get('attributes', [CongdapController::class, 'attributes'])->name('congdap.attributes');

      // Đặt apiResource sau các route cụ thể
      Route::apiResource('', CongdapController::class);
   });

   // Form 01A Report routes
   Route::prefix('reports')->group(function () {
      Route::get('form-01a', [Form01AReportController::class, 'index'])->name('reports.form01a');
      Route::get('form-01a/summary', [Form01AReportController::class, 'summary'])->name('reports.form01a.summary');
   });
});
