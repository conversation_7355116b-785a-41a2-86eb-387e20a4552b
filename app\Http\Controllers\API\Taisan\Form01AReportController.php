<?php

namespace App\Http\Controllers\API\Taisan;

use App\Http\Controllers\Controller;
use App\Http\Requests\Taisan\Form01AReportRequest;
use App\Http\Resources\Taisan\Form01AReportResource;
use App\Services\Form01AReportService;
use App\Traits\ApiResponseTrait;
use Illuminate\Support\Facades\Log;
use Throwable;

class Form01AReportController extends Controller
{
    use ApiResponseTrait;

    protected $reportService;

    public function __construct(Form01AReportService $reportService)
    {
        $this->reportService = $reportService;
    }

    /**
     * Generate Form 01A statistical report
     * 
     * @param Form01AReportRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Form01AReportRequest $request)
    {
        try {
            $filters = $request->validated();
            $perPage = $request->input('per_page', 50);

            $reportData = $this->reportService->generateReport($filters, $perPage);

            return $this->paginatedResponse(
                paginator: $reportData,
                resource: Form01AReportResource::class,
                message: 'Tạo báo cáo Form 01A thành công'
            );

        } catch (Throwable $e) {
            Log::error('Error generating Form 01A report: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi tạo báo cáo Form 01A', 500);
        }
    }

    /**
     * Generate Form 01A statistical summary
     * 
     * @param Form01AReportRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function summary(Form01AReportRequest $request)
    {
        try {
            $filters = $request->validated();
            
            $summaryData = $this->reportService->generateSummary($filters);

            return $this->successResponse(
                data: $summaryData,
                message: 'Tạo tổng hợp báo cáo Form 01A thành công'
            );

        } catch (Throwable $e) {
            Log::error('Error generating Form 01A summary: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi tạo tổng hợp báo cáo Form 01A', 500);
        }
    }
}
