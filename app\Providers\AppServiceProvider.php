<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Repositories\Contracts\CongdapRepositoryInterface;
use App\Repositories\CongdapRepository;
use App\Repositories\Contracts\Form01AReportRepositoryInterface;
use App\Repositories\Form01AReportRepository;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind(CongdapRepositoryInterface::class, CongdapRepository::class);
        $this->app->bind(Form01AReportRepositoryInterface::class, Form01AReportRepository::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
