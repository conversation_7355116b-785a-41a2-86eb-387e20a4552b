<template>
  <div class="export-report">
    <div class="report-filters">
      <div class="date-range">
        <label>Từ ngày:</label>
        <input type="date" v-model="startDate" class="date-input" />
        <label>Đến ngày:</label>
        <input type="date" v-model="endDate" class="date-input" />
      </div>
      <select v-model="reportType" class="report-type">
        <option value="">Loại báo cáo</option>
        <option value="b1bb">Mẫu số 01/BB - Biên bản bàn giao, tiếp nhận TSKCHTTHL </option>
        <option value="b1dm">Mẫu số 01/DM - Danh mục giao TSKCHTTHL </option>
        <option value="b2dm">Mẫu số 02/DM - Danh mục khai thác TSKCHTTHL </option>
        <option value="b1a">Mẫu số 01A - <PERSON>áo cáo kê khai TSKCHTTHL </option>
        <option value="b1b">Mẫu số 01B - Báo cáo kê khai bổ sung thông tin về TSKCHTTHL </option>
        <option value="b1c">Mẫu số 01C - Báo cáo kê khai tăng TSKCHTTHL </option>
        <option value="b1d">Mẫu số 01D - Báo cáo kê khai giảm TSKCHTTHL </option>
        <option value="b2a">Mẫu số 02A - Báo cáo tình hình khai thác TSKCHTTHL (Phương thức: Trực tiếp<!--  tổ chức thực hiện khai thác tài sản -->) </option>
        <option value="b2b">Mẫu số 02B - Báo cáo tình hình khai thác TSKCHTTHL (Phương thức: Cho thuê<!--  quyền khai thác tài sản -->) </option>
        <option value="b2c">Mẫu số 02C - Báo cáo tình hình khai thác TSKCHTTHL (Phương thức: Chuyển nhượng<!--  có thời hạn quyền khai thác tài sản -->) </option>
        <option value="b3a">Mẫu số 03A - Báo cáo tổng hợp tình hình quản lý, sử dụng và khai thác TSKCHTTHL </option>
        <option value="b3b">Mẫu số 03B - Báo cáo tổng hợp tình hình khai thác TSKCHTTHL </option>
        <option value="b3c">Mẫu số 03C - Báo cáo tổng hợp tình hình xử lý TSKCHTTHL </option>
        <option value="b3d">Mẫu số 03D - Báo cáo tổng hợp tình hình tăng, giảm TSKCHTTHL </option>
        <option value="b3đ">Mẫu số 03Đ - Báo cáo tổng hợp tình hình quản lý, sử dụng số tiền thu từ khai thác TSKCHTTHL </option>
        <option value="b3e">Mẫu số 03E - Báo cáo tổng hợp tình hình quản lý, sử dụng số tiền thu từ xử lý TSKCHTTHL </option>
      </select>
      <button @click="generateReport" class="export-btn">Tạo báo cáo thống kê</button>
    </div>

    <div class="report-content">
      <div class="split-container">
        <!-- Phần bên trái: Biểu đồ và bảng số liệu -->
        <div class="left-panel">
          <div class="chart-container">
            <div v-if="!reportGenerated" class="chart-placeholder">
              Biểu đồ thống kê sẽ được hiển thị tại đây
            </div>
            <div v-else class="chart-content">
              <h3>Biểu đồ thống kê {{ getReportTypeName() }}</h3>
              <div class="chart-image">
                <!-- Đây là nơi sẽ hiển thị biểu đồ thống kê thực tế -->
                <div class="chart-placeholder">
                  Biểu đồ đang được tạo...
                </div>
              </div>
            </div>
          </div>

          <div class="report-table">
            <DataTable
              :columnDefs="columnDefs"
              :rowData="tableData"
              :maxHeight="'27vh'"
              @rowClicked="onRowClicked"
              ref="dataTableRef"
            />
          </div>
        </div>

        <!-- Phần bên phải: Xem trước báo cáo -->
        <div class="right-panel">
          <div class="preview-header">
            <h3>Xem trước báo cáo</h3>
            <div class="preview-actions">
              <select v-model="exportFormat" class="format-select">
                <option value="pdf">PDF</option>
                <option value="excel">Excel</option>
                <option value="word">Word</option>
              </select>
              <button @click="exportReportFile" class="export-file-btn" :disabled="!reportGenerated">
                Tải xuống
              </button>
            </div>
          </div>

          <div v-if="!reportGenerated" class="preview-placeholder">
            <div class="placeholder-content">
              <div class="placeholder-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <polyline points="14 2 14 8 20 8"></polyline>
                  <line x1="16" y1="13" x2="8" y2="13"></line>
                  <line x1="16" y1="17" x2="8" y2="17"></line>
                  <polyline points="10 9 9 9 8 9"></polyline>
                </svg>
              </div>
              <p>Vui lòng chọn loại báo cáo và nhấn "Xuất báo cáo" để xem trước</p>
            </div>
          </div>

          <div v-else class="preview-document">
            <div class="document-header">
              <div class="document-logo">
                <div class="logo-placeholder">LOGO</div>
              </div>
              <div class="document-title">
                <h4>BÁO CÁO THỐNG KÊ</h4>
                <p>{{ getReportTypeName() }}</p>
                <p>Từ ngày: {{ formatDate(startDate) }} - Đến ngày: {{ formatDate(endDate) }}</p>
              </div>
            </div>

            <div class="document-content">
              <div class="document-section">
                <h5>I. THÔNG TIN CHUNG</h5>
                <p><strong>Đơn vị báo cáo:</strong> Sở Nông nghiệp và Phát triển Nông thôn</p>
                <p><strong>Thời gian báo cáo:</strong> {{ formatDate(startDate) }} - {{ formatDate(endDate) }}</p>
                <p><strong>Loại báo cáo:</strong> {{ getReportTypeName() }}</p>
              </div>

              <div class="document-section">
                <h5>II. BẢNG THỐNG KÊ</h5>
                <div class="document-table-container">
                  <table class="document-table">
                    <thead>
                      <tr>
                        <th>STT</th>
                        <th>Mã tài sản</th>
                        <th>Tên tài sản</th>
                        <th>Đơn vị quản lý</th>
                        <th>Ngày đưa vào sử dụng</th>
                        <th>Nguyên giá</th>
                        <th>Giá trị còn lại</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(item, index) in reportData" :key="index">
                        <td>{{ index + 1 }}</td>
                        <td>{{ item.code }}</td>
                        <td>{{ item.name }}</td>
                        <td>{{ item.unit }}</td>
                        <td>{{ item.startDate }}</td>
                        <td>{{ formatCurrency(item.originalPrice) }}</td>
                        <td>{{ formatCurrency(item.currentValue) }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <div class="document-section">
                <h5>III. BIỂU ĐỒ THỐNG KÊ</h5>
                <div class="document-chart">
                  <div class="chart-placeholder">
                    Biểu đồ thống kê
                  </div>
                </div>
              </div>

              <div class="document-footer">
                <div class="signature-section">
                  <p class="signature-date">Ngày ... tháng ... năm ...</p>
                  <p class="signature-title">NGƯỜI LẬP BÁO CÁO</p>
                  <p class="signature-name">(Ký, ghi rõ họ tên)</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import DataTable from '@/components/DataTable.vue'

const reportType = ref('')
const startDate = ref('')
const endDate = ref('')
const reportGenerated = ref(false)
const exportFormat = ref('pdf')
const dataTableRef = ref(null)

// Định nghĩa cấu trúc cột cho DataTable
const columnDefs = ref([
  {
    headerName: 'STT',
    valueGetter: (params: any) => params.node.rowIndex + 1,
    width: 70,
    minWidth: 70,
    maxWidth: 70
  },
  {
    headerName: 'Mã tài sản',
    field: 'code',
    minWidth: 100
  },
  {
    headerName: 'Tên tài sản',
    field: 'name',
    minWidth: 200,
    flex: 1
  },
  {
    headerName: 'Đơn vị quản lý',
    field: 'unit',
    minWidth: 200,
    flex: 1
  },
  {
    headerName: 'Ngày đưa vào sử dụng',
    field: 'startDate',
    minWidth: 150
  },
  {
    headerName: 'Nguyên giá',
    field: 'originalPrice',
    valueFormatter: (params: any) => formatCurrency(params.value),
    minWidth: 150
  },
  {
    headerName: 'Giá trị còn lại',
    field: 'currentValue',
    valueFormatter: (params: any) => formatCurrency(params.value),
    minWidth: 150
  }
])

// Dữ liệu mẫu cho bảng khi chưa tạo báo cáo
const defaultData = ref([
  {
    code: 'TS001',
    name: 'Tài sản 1',
    unit: 'Đơn vị 1',
    startDate: '2020-01-01',
    originalPrice: 120,
    currentValue: 95
  },
  {
    code: 'TS002',
    name: 'Tài sản 2',
    unit: 'Đơn vị 2',
    startDate: '2020-01-02',
    originalPrice: 140,
    currentValue: 110
  },
  {
    code: 'TS003',
    name: 'Tài sản 3',
    unit: 'Đơn vị 3',
    startDate: '2020-01-03',
    originalPrice: 160,
    currentValue: 125
  },
  {
    code: 'TS004',
    name: 'Tài sản 4',
    unit: 'Đơn vị 4',
    startDate: '2020-01-04',
    originalPrice: 180,
    currentValue: 140
  },
  {
    code: 'TS005',
    name: 'Tài sản 5',
    unit: 'Đơn vị 5',
    startDate: '2020-01-05',
    originalPrice: 200,
    currentValue: 155
  }
])

// Computed để quyết định dữ liệu hiển thị trong bảng
const tableData = computed(() => {
  return reportGenerated.value ? reportData.value : defaultData.value
})

// Xử lý sự kiện khi click vào dòng trong bảng
const onRowClicked = (event: any) => {
  console.log('Row clicked:', event.data)
  // Có thể thêm logic xử lý khi click vào dòng ở đây
}

// Dữ liệu mẫu cho báo cáo
const reportData = ref([
  {
    code: 'TS001',
    name: 'Đập thủy lợi Dầu Tiếng',
    unit: 'Công ty TNHH MTV Khai thác thủy lợi Dầu Tiếng - Phước Hòa',
    startDate: '2010-05-15',
    originalPrice: 1250000000,
    currentValue: 875000000
  },
  {
    code: 'TS002',
    name: 'Kênh chính Tây',
    unit: 'Công ty TNHH MTV Khai thác thủy lợi Dầu Tiếng - Phước Hòa',
    startDate: '2012-08-20',
    originalPrice: 750000000,
    currentValue: 562500000
  },
  {
    code: 'TS003',
    name: 'Trạm bơm Tân Hưng',
    unit: 'Công ty TNHH MTV Khai thác thủy lợi Miền Nam',
    startDate: '2015-03-10',
    originalPrice: 450000000,
    currentValue: 382500000
  },
  {
    code: 'TS004',
    name: 'Cống Bình Điền',
    unit: 'Công ty TNHH MTV Khai thác thủy lợi Miền Nam',
    startDate: '2014-07-22',
    originalPrice: 680000000,
    currentValue: 510000000
  },
  {
    code: 'TS005',
    name: 'Kênh Đông',
    unit: 'Công ty TNHH MTV Khai thác công trình thủy lợi Bắc',
    startDate: '2011-11-30',
    originalPrice: 920000000,
    currentValue: 644000000
  }
])

// Hàm tạo báo cáo
function generateReport() {
  // Kiểm tra xem đã chọn loại báo cáo chưa
  if (!reportType.value) {
    alert('Vui lòng chọn loại báo cáo')
    return
  }

  // Trong thực tế, đây sẽ là nơi gọi API để lấy dữ liệu báo cáo
  reportGenerated.value = true

  // Hiển thị thông báo
  alert(`Đã tạo báo cáo ${getReportTypeName()}`)
}

// Hàm lấy tên loại báo cáo từ giá trị đã chọn
function getReportTypeName() {
  const reportTypes: Record<string, string> = {
    'b1bb': 'Mẫu số 01/BB - Biên bản bàn giao, tiếp nhận TSKCHTTHL',
    'b1dm': 'Mẫu số 01/DM - Danh mục giao TSKCHTTHL',
    'b2dm': 'Mẫu số 02/DM - Danh mục khai thác TSKCHTTHL',
    'b1a': 'Mẫu số 01A - Báo cáo kê khai TSKCHTTHL',
    'b1b': 'Mẫu số 01B - Báo cáo kê khai bổ sung thông tin về TSKCHTTHL',
    'b1c': 'Mẫu số 01C - Báo cáo kê khai tăng TSKCHTTHL',
    'b1d': 'Mẫu số 01D - Báo cáo kê khai giảm TSKCHTTHL',
    'b2a': 'Mẫu số 02A - Báo cáo tình hình khai thác TSKCHTTHL (Phương thức: Trực tiếp tổ chức thực hiện khai thác tài sản)',
    'b2b': 'Mẫu số 02B - Báo cáo tình hình khai thác TSKCHTTHL (Phương thức: Cho thuê quyền khai thác tài sản)',
    'b2c': 'Mẫu số 02C - Báo cáo tình hình khai thác TSKCHTTHL (Phương thức: Chuyển nhượng có thời hạn quyền khai thác tài sản)',
    'b3a': 'Mẫu số 03A - Báo cáo tổng hợp tình hình quản lý, sử dụng và khai thác TSKCHTTHL',
    'b3b': 'Mẫu số 03B - Báo cáo tổng hợp tình hình khai thác TSKCHTTHL',
    'b3c': 'Mẫu số 03C - Báo cáo tổng hợp tình hình xử lý TSKCHTTHL',
    'b3d': 'Mẫu số 03D - Báo cáo tổng hợp tình hình tăng, giảm TSKCHTTHL',
    'b3đ': 'Mẫu số 03Đ - Báo cáo tổng hợp tình hình quản lý, sử dụng số tiền thu từ khai thác TSKCHTTHL',
    'b3e': 'Mẫu số 03E - Báo cáo tổng hợp tình hình quản lý, sử dụng số tiền thu từ xử lý TSKCHTTHL'
  }

  return reportType.value ? reportTypes[reportType.value] : 'Chưa chọn loại báo cáo'
}

// Hàm định dạng ngày tháng
function formatDate(dateString: string) {
  if (!dateString) return 'Chưa chọn'

  const date = new Date(dateString)
  return date.toLocaleDateString('vi-VN')
}

// Hàm định dạng tiền tệ
function formatCurrency(value: number) {
  return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(value)
}

// Hàm xuất báo cáo thành file
function exportReportFile() {
  if (!reportGenerated) {
    alert('Vui lòng tạo báo cáo trước khi tải xuống')
    return
  }

  // Trong thực tế, đây sẽ là nơi gọi API để tạo và tải xuống file báo cáo
  alert(`Đang tải xuống báo cáo dưới định dạng ${exportFormat.value.toUpperCase()}`)
}
</script>

<style scoped>
.export-report {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.report-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  align-items: center;
  flex-shrink: 0;
}

.date-range {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.date-input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.report-type {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 150px;
  max-width: 450px;
}

.export-btn {
  padding: 0.5rem 1rem;
  background-color: #4f46e5;
  color: white;
  border: 1px solid #4f46e5;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.export-btn:hover {
  opacity: 0.9;
}

.report-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Layout chia đôi màn hình */
.split-container {
  display: flex;
  flex: 1;
  gap: 1rem;
  overflow: hidden;
}

/* Panel bên trái */
.left-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-width: 0; /* Để tránh overflow */
}

/* Panel bên phải */
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  min-width: 0; /* Để tránh overflow */
}

.chart-container {
  background: white;
  padding: 1rem;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
  height: 350px;
  flex-shrink: 0;
}

.chart-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-content h3 {
  margin-bottom: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
}

.chart-image {
  flex: 1;
}

.chart-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  border: 2px dashed #ddd;
  border-radius: 4px;
}

.report-table {
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Phần xem trước báo cáo */
.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.preview-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.preview-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.format-select {
  padding: 0.4rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.export-file-btn {
  padding: 0.4rem 0.8rem;
  background-color: #4f46e5;
  color: white;
  border: 1px solid #4f46e5;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.export-file-btn:hover:not(:disabled) {
  opacity: 0.9;
}

.export-file-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.preview-placeholder {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.placeholder-content {
  text-align: center;
  color: #666;
}

.placeholder-icon {
  margin-bottom: 1rem;
  color: #aaa;
}

.preview-document {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
  padding: 1rem;
}

.document-header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
}

.document-logo {
  width: 80px;
  margin-right: 1rem;
}

.logo-placeholder {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-weight: bold;
  color: #666;
}

.document-title {
  flex: 1;
  text-align: center;
}

.document-title h4 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.document-title p {
  margin: 0.2rem 0;
  color: #555;
}

.document-content {
  flex: 1;
}

.document-section {
  margin-bottom: 1.5rem;
}

.document-section h5 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.8rem 0;
  color: #333;
}

.document-section p {
  margin: 0.3rem 0;
  color: #555;
}

.document-table-container {
  max-height: 200px;
  overflow-y: auto;
  margin: 1rem 0;
  border: 1px solid #ddd;
}

.document-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.document-table th,
.document-table td {
  padding: 0.5rem;
  text-align: left;
  border: 1px solid #ddd;
}

.document-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 1;
}

.document-chart {
  height: 150px;
  margin: 1rem 0;
}

.document-footer {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.signature-section {
  text-align: right;
  padding-right: 2rem;
}

.signature-date {
  margin-bottom: 3rem;
}

.signature-title {
  font-weight: 600;
  margin-bottom: 0.2rem;
}

.signature-name {
  font-style: italic;
  color: #666;
}
</style>
