[2025-05-27 03:57:40] local.ERROR: Method Illuminate\Database\Eloquent\Collection::setCurrentPage does not exist. {"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Database\\Eloquent\\Collection::setCurrentPage does not exist. at D:\\iGEO\\CTTL_HCM\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php:115)
[stacktrace]
#0 D:\\iGEO\\CTTL_HCM\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Support\\Collection->__call('setCurrentPage', Array)
#1 D:\\iGEO\\CTTL_HCM\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php(799): Illuminate\\Pagination\\AbstractPaginator->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Collection), 'setCurrentPage', Array)
#2 D:\\iGEO\\CTTL_HCM\\app\\debug_congdap_attributes.php(76): Illuminate\\Pagination\\AbstractPaginator->__call('setCurrentPage', Array)
#3 {main}
"} 
