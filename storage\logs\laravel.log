[2025-05-27 03:57:40] local.ERROR: Method Illuminate\Database\Eloquent\Collection::setCurrentPage does not exist. {"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Database\\Eloquent\\Collection::setCurrentPage does not exist. at D:\\iGEO\\CTTL_HCM\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php:115)
[stacktrace]
#0 D:\\iGEO\\CTTL_HCM\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Support\\Collection->__call('setCurrentPage', Array)
#1 D:\\iGEO\\CTTL_HCM\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php(799): Illuminate\\Pagination\\AbstractPaginator->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Collection), 'setCurrentPage', Array)
#2 D:\\iGEO\\CTTL_HCM\\app\\debug_congdap_attributes.php(76): Illuminate\\Pagination\\AbstractPaginator->__call('setCurrentPage', Array)
#3 {main}
"} 
[2025-05-27 07:08:47] local.ERROR: Error generating Form 01A summary: SQLSTATE[42803]: Grouping error: 7 ERROR:  column "combined_infrastructure.ten" must appear in the GROUP BY clause or be used in an aggregate function
LINE 85: ..."xa"."id")) as "combined_infrastructure" order by "ten" asc ...
                                                              ^ (Connection: pgsql, SQL: select 
                COUNT(*) as total_quantity,
                SUM(CASE WHEN loai_ct_full = 'Trạm bơm' THEN 1 ELSE 0 END) as trambom_count,
                SUM(CASE WHEN loai_ct_full = 'Cống đập' THEN 1 ELSE 0 END) as congdap_count,
                SUM(CASE WHEN loai_ct_full = 'Kênh mương' THEN 1 ELSE 0 END) as kenhmuong_count,
                SUM(COALESCE(nguyengia, 0)) as total_original_cost,
                SUM(COALESCE(gtcl, 0)) as total_remaining_value,
                SUM(COALESCE(khau_hao, 0)) as total_depreciation,
                AVG(COALESCE(dt_dat, 0)) as avg_land_area
             from ((select 
                t.id,
                t.ten,
                t.quymo_ct,
                1 as so_luong,
                'Trạm bơm' as loai_ct_full,
                t.nam_sd,
                t.dt_dat,
                COALESCE(qt.nguyengia, 0) as nguyengia,
                CASE
                    WHEN qt.nguyengia IS NOT NULL AND t.nam_sd IS NOT NULL
                    THEN (2025 - t.nam_sd) * 0.04 * qt.nguyengia
                    ELSE 0
                END as khau_hao,
                CASE
                    WHEN qt.nguyengia IS NOT NULL AND t.nam_sd IS NOT NULL
                    THEN qt.nguyengia - ((2025 - t.nam_sd) * 0.04 * qt.nguyengia)
                    ELSE COALESCE(qt.nguyengia, 0)
                END as gtcl,
                COALESCE(t.tinhtrang, 'Đang hoạt động') as tinhtrang,
                t.chuthich,
                xa.tenxa,
                xa.tenhuyen,
                t.dv_quanly,
                'trambom' as source_table
             from "taisan"."trambom" as "t" left join "taisan"."quyettoan" as "qt" on "t"."id_qt" = "qt"."id" left join "basemap"."rg_xa" as "xa" on "t"."id_xa" = "xa"."id") union (select 
                c.id,
                c.ten,
                c.quymo_ct,
                1 as so_luong,
                'Cống đập' as loai_ct_full,
                c.nam_sd,
                c.dt_dat,
                COALESCE(qt.nguyengia, 0) as nguyengia,
                CASE
                    WHEN qt.nguyengia IS NOT NULL AND c.nam_sd IS NOT NULL
                    THEN (2025 - c.nam_sd) * 0.04 * qt.nguyengia
                    ELSE 0
                END as khau_hao,
                CASE
                    WHEN qt.nguyengia IS NOT NULL AND c.nam_sd IS NOT NULL
                    THEN qt.nguyengia - ((2025 - c.nam_sd) * 0.04 * qt.nguyengia)
                    ELSE COALESCE(qt.nguyengia, 0)
                END as gtcl,
                COALESCE(c.tinhtrang, 'Đang hoạt động') as tinhtrang,
                c.chuthich,
                xa.tenxa,
                xa.tenhuyen,
                c.dv_quanly,
                'congdap' as source_table
             from "taisan"."congdap" as "c" left join "taisan"."quyettoan" as "qt" on "c"."id_qt" = "qt"."id" left join "basemap"."rg_xa" as "xa" on "c"."id_xa" = "xa"."id") union (select 
                k.id,
                k.ten,
                k.quymo_ct,
                1 as so_luong,
                'Kênh mương' as loai_ct_full,
                k.nam_sd,
                k.dt_dat,
                COALESCE(qt.nguyengia, 0) as nguyengia,
                CASE
                    WHEN qt.nguyengia IS NOT NULL AND k.nam_sd IS NOT NULL
                    THEN (2025 - k.nam_sd) * 0.04 * qt.nguyengia
                    ELSE 0
                END as khau_hao,
                CASE
                    WHEN qt.nguyengia IS NOT NULL AND k.nam_sd IS NOT NULL
                    THEN qt.nguyengia - ((2025 - k.nam_sd) * 0.04 * qt.nguyengia)
                    ELSE COALESCE(qt.nguyengia, 0)
                END as gtcl,
                COALESCE(k.tinhtrang, 'Đang hoạt động') as tinhtrang,
                k.chuthich,
                xa.tenxa,
                xa.tenhuyen,
                k.dv_quanly,
                'kenhmuong' as source_table
             from "taisan"."kenhmuong" as "k" left join "taisan"."quyettoan" as "qt" on "k"."id_qt" = "qt"."id" left join "basemap"."rg_xa" as "xa" on "k"."id_xa" = "xa"."id")) as "combined_infrastructure" order by "ten" asc limit 1)  
[2025-05-27 07:09:20] local.ERROR: Error generating Form 01A summary: SQLSTATE[42803]: Grouping error: 7 ERROR:  column "combined_infrastructure.ten" must appear in the GROUP BY clause or be used in an aggregate function
LINE 85: ..."xa"."id")) as "combined_infrastructure" order by "ten" asc ...
                                                              ^ (Connection: pgsql, SQL: select 
                COUNT(*) as total_quantity,
                SUM(CASE WHEN loai_ct_full = 'Trạm bơm' THEN 1 ELSE 0 END) as trambom_count,
                SUM(CASE WHEN loai_ct_full = 'Cống đập' THEN 1 ELSE 0 END) as congdap_count,
                SUM(CASE WHEN loai_ct_full = 'Kênh mương' THEN 1 ELSE 0 END) as kenhmuong_count,
                SUM(COALESCE(nguyengia, 0)) as total_original_cost,
                SUM(COALESCE(gtcl, 0)) as total_remaining_value,
                SUM(COALESCE(khau_hao, 0)) as total_depreciation,
                AVG(COALESCE(dt_dat, 0)) as avg_land_area
             from ((select 
                t.id,
                t.ten,
                t.quymo_ct,
                1 as so_luong,
                'Trạm bơm' as loai_ct_full,
                t.nam_sd,
                t.dt_dat,
                COALESCE(qt.nguyengia, 0) as nguyengia,
                CASE
                    WHEN qt.nguyengia IS NOT NULL AND t.nam_sd IS NOT NULL
                    THEN (2025 - t.nam_sd) * 0.04 * qt.nguyengia
                    ELSE 0
                END as khau_hao,
                CASE
                    WHEN qt.nguyengia IS NOT NULL AND t.nam_sd IS NOT NULL
                    THEN qt.nguyengia - ((2025 - t.nam_sd) * 0.04 * qt.nguyengia)
                    ELSE COALESCE(qt.nguyengia, 0)
                END as gtcl,
                COALESCE(t.tinhtrang, 'Đang hoạt động') as tinhtrang,
                t.chuthich,
                xa.tenxa,
                xa.tenhuyen,
                t.dv_quanly,
                'trambom' as source_table
             from "taisan"."trambom" as "t" left join "taisan"."quyettoan" as "qt" on "t"."id_qt" = "qt"."id" left join "basemap"."rg_xa" as "xa" on "t"."id_xa" = "xa"."id") union (select 
                c.id,
                c.ten,
                c.quymo_ct,
                1 as so_luong,
                'Cống đập' as loai_ct_full,
                c.nam_sd,
                c.dt_dat,
                COALESCE(qt.nguyengia, 0) as nguyengia,
                CASE
                    WHEN qt.nguyengia IS NOT NULL AND c.nam_sd IS NOT NULL
                    THEN (2025 - c.nam_sd) * 0.04 * qt.nguyengia
                    ELSE 0
                END as khau_hao,
                CASE
                    WHEN qt.nguyengia IS NOT NULL AND c.nam_sd IS NOT NULL
                    THEN qt.nguyengia - ((2025 - c.nam_sd) * 0.04 * qt.nguyengia)
                    ELSE COALESCE(qt.nguyengia, 0)
                END as gtcl,
                COALESCE(c.tinhtrang, 'Đang hoạt động') as tinhtrang,
                c.chuthich,
                xa.tenxa,
                xa.tenhuyen,
                c.dv_quanly,
                'congdap' as source_table
             from "taisan"."congdap" as "c" left join "taisan"."quyettoan" as "qt" on "c"."id_qt" = "qt"."id" left join "basemap"."rg_xa" as "xa" on "c"."id_xa" = "xa"."id") union (select 
                k.id,
                k.ten,
                k.quymo_ct,
                1 as so_luong,
                'Kênh mương' as loai_ct_full,
                k.nam_sd,
                k.dt_dat,
                COALESCE(qt.nguyengia, 0) as nguyengia,
                CASE
                    WHEN qt.nguyengia IS NOT NULL AND k.nam_sd IS NOT NULL
                    THEN (2025 - k.nam_sd) * 0.04 * qt.nguyengia
                    ELSE 0
                END as khau_hao,
                CASE
                    WHEN qt.nguyengia IS NOT NULL AND k.nam_sd IS NOT NULL
                    THEN qt.nguyengia - ((2025 - k.nam_sd) * 0.04 * qt.nguyengia)
                    ELSE COALESCE(qt.nguyengia, 0)
                END as gtcl,
                COALESCE(k.tinhtrang, 'Đang hoạt động') as tinhtrang,
                k.chuthich,
                xa.tenxa,
                xa.tenhuyen,
                k.dv_quanly,
                'kenhmuong' as source_table
             from "taisan"."kenhmuong" as "k" left join "taisan"."quyettoan" as "qt" on "k"."id_qt" = "qt"."id" left join "basemap"."rg_xa" as "xa" on "k"."id_xa" = "xa"."id")) as "combined_infrastructure" order by "ten" asc limit 1)  
