<template>
    <div class="flex flex-col h-screen overflow-hidden">
        <!-- Custom Header -->
        <header class="h-[6vh] w-full bg-white shadow-md fixed top-0 z-50 flex items-center px-2 justify-between">
            <div class="flex items-center gap-2">
                <Button variant="ghost" size="icon" class="h-7 w-7" @click="toggleSidebar">
                    <PanelLeft />
                    <span class="sr-only">Toggle Sidebar</span>
                </Button>
                <AppLogo />
            </div>
            <nav class="flex items-center gap-6">
                <Link v-for="item in navLinks" :key="item.href" :href="item.href"
                    class="text-gray-700 font-medium hover:text-blue-600"
                    :class="{ 'text-blue-600': page.url === item.href }">
                {{ item.label }}
                </Link>
            </nav>
            <div class="relative">
                <DropdownMenu>
                    <DropdownMenuTrigger as-child>
                        <button class="flex items-center space-x-2 px-3 py-2 rounded hover:bg-gray-100">
                            <Avatar class="overflow-hidden rounded-full">
                                <AvatarImage v-if="user?.avatar" :src="user.avatar" :alt="user.name" />
                                <AvatarFallback class="rounded-lg text-black dark:text-white">
                                    {{ getInitials(user?.name) }}
                                </AvatarFallback>
                            </Avatar>
                            <span class="font-medium text-gray-800">{{ user?.name }}</span>
                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" class="w-64">
                        <UserMenuContent :user="user" />
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>
        </header>

        <div class="relative mt-[6vh] h-[91vh] overflow-hidden">
            <!-- Sidebar -->
            <aside
                class="absolute top-0 left-0 h-full w-[18%] bg-gray-50 shadow-md overflow-y-auto transition-transform duration-500 ease-in-out z-40"
                :class="isSidebarOpen ? 'translate-x-0' : '-translate-x-full'">
                <slot name="sidebar"></slot>
            </aside>

            <!-- Main content area -->
            <main class="h-full overflow-y-auto transition-all duration-500 ease-in-out" :class="[
                page.url === '/' ? 'w-full' : 'p-4',
                isSidebarOpen && page.url !== '/' ? 'ml-[20%] w-[80%]' : 'ml-0 w-full'
            ]">
                <slot :isSidebarOpen="isSidebarOpen"></slot>
                <slot name="isSidebarOpen" :isSidebarOpen="isSidebarOpen"></slot>
            </main>
        </div>

        <!-- Footer -->
        <footer class="h-[3vh] bg-gray-50 flex items-center justify-center px-4 shadow-md fixed bottom-0 w-full">
            <p class="text-gray-600">&copy; 2024 iGEO. All rights reserved.</p>
        </footer>
    </div>
</template>

<script setup lang="ts">
import { Link, usePage } from '@inertiajs/vue3'
import UserMenuContent from '@/components/UserMenuContent.vue'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { getInitials } from '@/composables/useInitials'
import { ref, computed } from 'vue'
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent } from '@/components/ui/dropdown-menu'
import AppLogo from '@/components/AppLogo.vue'
import { Button } from '@/components/ui/button';
import { PanelLeft } from 'lucide-vue-next'

const page = usePage()
const user = computed(() => (page.props.auth as any)?.user)
const isSidebarOpen = ref(true)

function toggleSidebar() {
    isSidebarOpen.value = !isSidebarOpen.value
}

const navLinks = [
    { label: 'Bản đồ', href: '/' },
    { label: 'Quản lý dữ liệu', href: '/data-management' },
    { label: 'Báo cáo thống kê', href: '/reports' },
    { label: 'Quản trị', href: '/admin' }
]
const showMenu = ref(false)

function closeMenu() {
    showMenu.value = false
}
</script>