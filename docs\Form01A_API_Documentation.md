# Form 01A Statistical Reporting API Documentation

## Overview
The Form 01A Statistical Reporting API provides comprehensive statistical reports for infrastructure assets including pumping stations (trạm bơm), culverts/dams (cống đập), and canals/channels (kênh mương).

## Endpoints

### 1. Generate Form 01A Report
**GET** `/api/taisan/reports/form-01a`

Returns paginated infrastructure data with calculated depreciation values.

#### Query Parameters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `ten_doituong` | string | Object name filter | `Trạm bơm ABC` |
| `ma_donvi` | string | Unit code filter | `DV001` |
| `thon_xom` | string | Village/hamlet filter | `Thôn 1` |
| `xa_phuong` | string | Ward/commune filter | `Xã An Phú` |
| `quan_huyen` | string | District filter | `Huyện Cần Giờ` |
| `tinh_thanh` | string | Province/city filter | `TP.HCM` |
| `id_xa` | string | Ward ID filter | `79001` |
| `loai_ct` | string | Infrastructure type code | `TB001` |
| `infrastructure_types` | array | Infrastructure types | `["trambom","congdap"]` |
| `nam_tu` | integer | Year from | `2010` |
| `nam_den` | integer | Year to | `2023` |
| `search` | string | General search term | `bơm nước` |
| `user_name` | string | User name for report | `Nguyễn Văn A` |
| `user_phone` | string | User phone | `0901234567` |
| `user_email` | string | User email | `<EMAIL>` |
| `per_page` | integer | Items per page (1-100) | `50` |

#### Example Request
```bash
GET /api/taisan/reports/form-01a?infrastructure_types[]=trambom&infrastructure_types[]=congdap&nam_tu=2015&nam_den=2023&per_page=20
```

#### Response Format
```json
{
  "success": true,
  "message": "Tạo báo cáo Form 01A thành công",
  "data": [
    {
      "id": "T0001",
      "ten": "Trạm bơm An Phú",
      "quy_mo": "50 m³/h",
      "so_luong": 1,
      "loai_cong_trinh": "Trạm bơm",
      "nam_su_dung": 2015,
      "dien_tich_dat": "500.00",
      "nguyen_gia": "2.5 tỷ VNĐ",
      "khau_hao_tinh": "800 triệu VNĐ",
      "gtcl": "1.7 tỷ VNĐ",
      "tinh_trang": "Đang hoạt động",
      "ghi_chu": "Hoạt động tốt",
      "administrative_info": {
        "ten_xa": "Xã An Phú",
        "ten_huyen": "Huyện Cần Giờ",
        "don_vi_quan_ly": "Công ty TNHH MTV Khai thác thủy lợi"
      },
      "metadata": {
        "source_table": "trambom",
        "calculated_at": "2024-01-15T10:30:00.000Z"
      }
    }
  ],
  "meta": {
    "current_page": 1,
    "per_page": 20,
    "total": 150,
    "last_page": 8,
    "from": 1,
    "to": 20
  },
  "links": {
    "first": "http://localhost/api/taisan/reports/form-01a?page=1",
    "last": "http://localhost/api/taisan/reports/form-01a?page=8",
    "prev": null,
    "next": "http://localhost/api/taisan/reports/form-01a?page=2"
  }
}
```

### 2. Generate Form 01A Summary
**GET** `/api/taisan/reports/form-01a/summary`

Returns statistical summary of infrastructure data.

#### Query Parameters
Same as the main report endpoint.

#### Example Request
```bash
GET /api/taisan/reports/form-01a/summary?infrastructure_types[]=trambom&nam_tu=2015
```

#### Response Format
```json
{
  "success": true,
  "message": "Tạo tổng hợp báo cáo Form 01A thành công",
  "data": {
    "total_quantity": 150,
    "infrastructure_breakdown": {
      "trambom": 45,
      "congdap": 65,
      "kenhmuong": 40
    },
    "financial_summary": {
      "total_original_cost": 125000000000,
      "total_remaining_value": 89500000000,
      "total_depreciation": 35500000000
    },
    "avg_land_area": 750.5,
    "generated_at": "2024-01-15T10:30:00.000Z",
    "user_info": {
      "name": "Nguyễn Văn A",
      "phone": "0901234567",
      "email": "<EMAIL>"
    },
    "applied_filters": {
      "infrastructure_types": ["trambom"],
      "year_range": {
        "from": 2015,
        "to": null
      }
    }
  }
}
```

## GTCL Calculation Formula
The remaining value (GTCL) is calculated using the depreciation formula:
```
GTCL = Original Cost - ((Current Year - Year of Use) × 4% × Original Cost)
```

Where:
- Current Year = 2024 (or current year)
- Year of Use = `nam_sd` field from database
- Original Cost = `nguyengia` from quyettoan table
- Depreciation Rate = 4% per year

## Infrastructure Types
- `trambom`: Pumping stations (Trạm bơm)
- `congdap`: Culverts/Dams (Cống đập)  
- `kenhmuong`: Canals/Channels (Kênh mương)

## Error Responses
```json
{
  "success": false,
  "message": "Error message",
  "code": 400,
  "errors": {
    "field_name": ["Validation error message"]
  }
}
```

## Status Codes
- `200`: Success
- `400`: Bad Request (validation errors)
- `422`: Unprocessable Entity (validation errors)
- `500`: Internal Server Error

## Notes
- All monetary values are formatted with appropriate Vietnamese currency units
- Land area is in square meters
- Default status is "Đang hoạt động" (Operating)
- Data is cached for 30 minutes for performance
- Maximum 100 items per page
