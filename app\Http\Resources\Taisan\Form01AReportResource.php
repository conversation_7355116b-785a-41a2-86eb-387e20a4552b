<?php

namespace App\Http\Resources\Taisan;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class Form01AReportResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'ten' => $this->ten, // Tên
            'quy_mo' => $this->quymo_ct, // Quy mô
            'so_luong' => $this->so_luong, // Số lượng
            'loai_cong_trinh' => $this->loai_ct_full, // Loại công trình
            'nam_su_dung' => $this->nam_sd, // Năm sử dụng
            'dien_tich_dat' => $this->formatNumber($this->dt_dat), // Diện tích đất
            'nguyen_gia' => $this->formatCurrency($this->nguyengia), // Nguyên giá
            'khau_hao_tinh' => $this->formatCurrency($this->khau_hao), // Khấu hao tính
            'gtcl' => $this->formatCurrency($this->gtcl), // GTCL (Remaining value)
            'tinh_trang' => $this->tinhtrang, // Tình trạng
            'ghi_chu' => $this->chuthich, // Ghi chú
            
            // Administrative information
            'administrative_info' => [
                'ten_xa' => $this->tenxa,
                'ten_huyen' => $this->tenhuyen,
                'don_vi_quan_ly' => $this->dv_quanly,
            ],
            
            // Additional metadata
            'metadata' => [
                'source_table' => $this->source_table,
                'calculated_at' => now()->toISOString(),
            ],
        ];
    }

    /**
     * Format number with appropriate decimal places
     * 
     * @param mixed $value
     * @return string|null
     */
    protected function formatNumber($value): ?string
    {
        if ($value === null || $value === '') {
            return null;
        }

        $numericValue = (float) $value;
        
        if ($numericValue == 0) {
            return '0';
        }

        // Format with 2 decimal places, remove trailing zeros
        return rtrim(rtrim(number_format($numericValue, 2, '.', ','), '0'), '.');
    }

    /**
     * Format currency values
     * 
     * @param mixed $value
     * @return string|null
     */
    protected function formatCurrency($value): ?string
    {
        if ($value === null || $value === '') {
            return null;
        }

        $numericValue = (float) $value;
        
        if ($numericValue == 0) {
            return '0 VNĐ';
        }

        // Format large numbers with appropriate units
        if ($numericValue >= 1000000000) {
            return number_format($numericValue / 1000000000, 2, '.', ',') . ' tỷ VNĐ';
        } elseif ($numericValue >= 1000000) {
            return number_format($numericValue / 1000000, 2, '.', ',') . ' triệu VNĐ';
        } elseif ($numericValue >= 1000) {
            return number_format($numericValue / 1000, 2, '.', ',') . ' nghìn VNĐ';
        } else {
            return number_format($numericValue, 0, '.', ',') . ' VNĐ';
        }
    }
}
