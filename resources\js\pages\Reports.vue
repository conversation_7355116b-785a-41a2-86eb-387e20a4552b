<template>
  <MainLayout>
    <template #sidebar>
      <div class="report-controls">
        <h3>Báo cáo thống kê</h3>
        <div class="control-group">
         <!--  <button class="control-btn">Tạo báo cáo mới</button>
          <button class="control-btn">L<PERSON><PERSON> báo cáo</button> -->
          <button
            class="control-btn"
            :class="{ 'active': activeView === 'export' }"
            @click="setActiveView('export')"
          >
            Xuất báo cáo thống kê
          </button>
          <button
            class="control-btn"
            :class="{ 'active': activeView === 'depreciation' }"
            @click="setActiveView('depreciation')"
          >
            Tính giá trị hao mòn
          </button>
        </div>
      </div>
    </template>

    <div class="reports-container">
      <!-- View xuất báo cáo thống kê -->
      <ExportReport v-if="activeView === 'export'" />

      <!-- View tính giá trị hao mòn -->
      <DepreciationCalculator v-else-if="activeView === 'depreciation'" />
    </div>
  </MainLayout>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import MainLayout from '@/layouts/MainLayout.vue'
import ExportReport from '@/components/reports/ExportReport.vue'
import DepreciationCalculator from '@/components/reports/DepreciationCalculator.vue'

// Biến lưu trạng thái view đang hiển thị
// 'export': View xuất báo cáo thống kê (mặc định)
// 'depreciation': View tính giá trị hao mòn
const activeView = ref('export')

// Hàm chuyển đổi giữa các view
function setActiveView(view: string) {
  activeView.value = view
}
</script>

<style scoped>
.reports-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.report-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  align-items: center;
  flex-shrink: 0;
}

.date-range {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.date-input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.report-type {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 150px;
}

.report-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
  flex-shrink: 0;
}

.stat-card {
  background: white;
  padding: 1rem;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-card h4 {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-top: 0.5rem;
}

.chart-container {
  background: white;
  padding: 1rem;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
  height: 200px;
  flex-shrink: 0;
}

.chart-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  border: 2px dashed #ddd;
  border-radius: 4px;
}

.report-table {
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: auto;
  flex: 1;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th,
td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #eee;
}

th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.control-btn {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

.control-btn:hover {
  background-color: #f8f9fa;
}

.control-btn.active {
  background-color: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

.report-controls {
  padding: 1rem;
}

.report-controls h3 {
  margin-bottom: 1rem;
  color: #333;
}
</style>