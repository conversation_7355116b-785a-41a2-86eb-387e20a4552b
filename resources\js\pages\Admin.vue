<template>
  <MainLayout>
    <template #sidebar>
      <div class="p-4">
        <h3 class="text-lg font-medium text-gray-700 mb-4">Quản tr<PERSON> hệ thống</h3>
        <div class="flex flex-col gap-2">
          <button class="p-2 border border-gray-200 rounded bg-white hover:bg-gray-50 transition-colors">Quản lý người dùng</button>
          <button class="p-2 border border-gray-200 rounded bg-white hover:bg-gray-50 transition-colors">Phân quyền</button>
          <button class="p-2 border border-gray-200 rounded bg-white hover:bg-gray-50 transition-colors">C<PERSON><PERSON> h<PERSON>nh hệ thống</button>
          <button class="p-2 border border-gray-200 rounded bg-white hover:bg-gray-50 transition-colors">Nhật ký hoạt động</button>
        </div>
      </div>
    </template>

    <div class="h-full flex flex-col">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold text-gray-800">Q<PERSON><PERSON><PERSON> lý người dùng</h2>
        <button class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">Thêm người dùng</button>
      </div>

      <div class="flex gap-4 mb-4">
        <input type="text" placeholder="Tìm kiếm người dùng..." class="flex-1 p-2 border border-gray-200 rounded" />
        <select class="p-2 border border-gray-200 rounded min-w-[150px]">
          <option value="">Tất cả vai trò</option>
          <option value="admin">Quản trị viên</option>
          <option value="user">Người dùng</option>
          <option value="editor">Biên tập viên</option>
        </select>
        <select class="p-2 border border-gray-200 rounded min-w-[150px]">
          <option value="">Tất cả trạng thái</option>
          <option value="active">Đang hoạt động</option>
          <option value="inactive">Không hoạt động</option>
        </select>
      </div>

      <div class="bg-white rounded shadow-sm overflow-auto flex-1">
        <table class="w-full">
          <thead>
            <tr class="bg-gray-50">
              <th class="p-3 text-left font-semibold">ID</th>
              <th class="p-3 text-left font-semibold">Tên người dùng</th>
              <th class="p-3 text-left font-semibold">Email</th>
              <th class="p-3 text-left font-semibold">Vai trò</th>
              <th class="p-3 text-left font-semibold">Trạng thái</th>
              <th class="p-3 text-left font-semibold">Ngày tạo</th>
              <th class="p-3 text-left font-semibold">Thao tác</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="i in 5" :key="i" class="border-b border-gray-100">
              <td class="p-3">{{ i }}</td>
              <td class="p-3">User {{ i }}</td>
              <td class="p-3">user{{ i }}@example.com</td>
              <td class="p-3">
                <span :class="['px-2 py-1 rounded text-sm', i === 1 ? 'bg-blue-600 text-white' : 'bg-gray-600 text-white']">
                  {{ i === 1 ? 'Quản trị viên' : 'Người dùng' }}
                </span>
              </td>
              <td class="p-3">
                <span :class="['px-2 py-1 rounded text-sm', i % 2 === 0 ? 'bg-green-600 text-white' : 'bg-red-600 text-white']">
                  {{ i % 2 === 0 ? 'Đang hoạt động' : 'Không hoạt động' }}
                </span>
              </td>
              <td class="p-3">2024-03-{{ i }}</td>
              <td class="p-3">
                <button class="px-2 py-1 bg-blue-600 text-white rounded mr-2 hover:bg-blue-700">Sửa</button>
                <button class="px-2 py-1 bg-red-600 text-white rounded hover:bg-red-700">Xóa</button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </MainLayout>
</template>

<script setup lang="ts">
import MainLayout from '@/layouts/MainLayout.vue'
</script>

<style scoped>
.admin-container {
  padding: 1rem;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-shrink: 0;
}

.admin-header h2 {
  margin: 0;
  color: #333;
}

.add-btn {
  padding: 0.5rem 1rem;
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.admin-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-shrink: 0;
}

.search-input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  flex: 1;
}

.role-filter,
.status-filter {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 150px;
}

.admin-table {
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: auto;
  flex: 1;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #eee;
}

th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.role-badge,
.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
}

.role-badge.admin {
  background-color: #007bff;
  color: white;
}

.role-badge.user {
  background-color: #6c757d;
  color: white;
}

.status-badge.active {
  background-color: #28a745;
  color: white;
}

.status-badge.inactive {
  background-color: #dc3545;
  color: white;
}

.action-btn {
  padding: 0.25rem 0.5rem;
  border: none;
  border-radius: 4px;
  background-color: #007bff;
  color: white;
  cursor: pointer;
  margin-right: 0.5rem;
}

.action-btn.delete {
  background-color: #dc3545;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.control-btn {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

.control-btn:hover {
  background-color: #f8f9fa;
}

.admin-controls {
  padding: 1rem;
}

.admin-controls h3 {
  margin-bottom: 1rem;
  color: #333;
}
</style> 