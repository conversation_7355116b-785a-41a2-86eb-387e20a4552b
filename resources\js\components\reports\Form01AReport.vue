<template>
  <div class="form-01a-report">
    <div class="report-header">
      <h2><PERSON><PERSON><PERSON> c<PERSON>o thống kê Form 01A</h2>
      <p><PERSON><PERSON><PERSON> cáo tài sản hạ tầng thủy lợi</p>
    </div>

    <!-- Filter Form -->
    <div class="filter-section">
      <form @submit.prevent="generateReport" class="filter-form">
        <div class="form-row">
          <div class="form-group">
            <label>Tên đối tượng:</label>
            <input v-model="filters.ten_doituong" type="text" placeholder="Nhập tên công trình">
          </div>
          <div class="form-group">
            <label>Xã/Phường:</label>
            <input v-model="filters.xa_phuong" type="text" placeholder="Nhập tên xã/phường">
          </div>
          <div class="form-group">
            <label>Quận/Huyện:</label>
            <input v-model="filters.quan_huyen" type="text" placeholder="Nhập tên quận/huyện">
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>Loại công trình:</label>
            <select v-model="filters.infrastructure_types" multiple>
              <option value="trambom">Trạm bơm</option>
              <option value="congdap">Cống đập</option>
              <option value="kenhmuong">Kênh mương</option>
            </select>
          </div>
          <div class="form-group">
            <label>Năm từ:</label>
            <input v-model="filters.nam_tu" type="number" min="1900" :max="currentYear">
          </div>
          <div class="form-group">
            <label>Năm đến:</label>
            <input v-model="filters.nam_den" type="number" min="1900" :max="currentYear">
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>Tên người tạo:</label>
            <input v-model="filters.user_name" type="text" placeholder="Nhập tên">
          </div>
          <div class="form-group">
            <label>Điện thoại:</label>
            <input v-model="filters.user_phone" type="tel" placeholder="Nhập số điện thoại">
          </div>
          <div class="form-group">
            <label>Email:</label>
            <input v-model="filters.user_email" type="email" placeholder="Nhập email">
          </div>
        </div>

        <div class="form-actions">
          <button type="submit" :disabled="loading" class="btn-primary">
            {{ loading ? 'Đang tạo báo cáo...' : 'Tạo báo cáo' }}
          </button>
          <button type="button" @click="generateSummary" :disabled="loading" class="btn-secondary">
            Tạo tổng hợp
          </button>
          <button type="button" @click="resetFilters" class="btn-outline">
            Đặt lại
          </button>
        </div>
      </form>
    </div>

    <!-- Summary Section -->
    <div v-if="summary" class="summary-section">
      <h3>Tổng hợp thống kê</h3>
      <div class="summary-cards">
        <div class="summary-card">
          <h4>Tổng số lượng</h4>
          <p class="summary-number">{{ summary.total_quantity }}</p>
        </div>
        <div class="summary-card">
          <h4>Trạm bơm</h4>
          <p class="summary-number">{{ summary.infrastructure_breakdown.trambom }}</p>
        </div>
        <div class="summary-card">
          <h4>Cống đập</h4>
          <p class="summary-number">{{ summary.infrastructure_breakdown.congdap }}</p>
        </div>
        <div class="summary-card">
          <h4>Kênh mương</h4>
          <p class="summary-number">{{ summary.infrastructure_breakdown.kenhmuong }}</p>
        </div>
      </div>
      <div class="financial-summary">
        <h4>Tổng hợp tài chính</h4>
        <p>Tổng nguyên giá: {{ formatCurrency(summary.financial_summary.total_original_cost) }}</p>
        <p>Tổng GTCL: {{ formatCurrency(summary.financial_summary.total_remaining_value) }}</p>
        <p>Tổng khấu hao: {{ formatCurrency(summary.financial_summary.total_depreciation) }}</p>
      </div>
    </div>

    <!-- Report Data Table -->
    <div v-if="reportData.length > 0" class="report-table-section">
      <h3>Dữ liệu báo cáo</h3>
      <div class="table-container">
        <table class="report-table">
          <thead>
            <tr>
              <th>Tên</th>
              <th>Quy mô</th>
              <th>Loại công trình</th>
              <th>Năm sử dụng</th>
              <th>Diện tích đất</th>
              <th>Nguyên giá</th>
              <th>GTCL</th>
              <th>Tình trạng</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in reportData" :key="item.id">
              <td>{{ item.ten }}</td>
              <td>{{ item.quy_mo }}</td>
              <td>{{ item.loai_cong_trinh }}</td>
              <td>{{ item.nam_su_dung }}</td>
              <td>{{ item.dien_tich_dat }}</td>
              <td>{{ item.nguyen_gia }}</td>
              <td>{{ item.gtcl }}</td>
              <td>{{ item.tinh_trang }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div v-if="pagination" class="pagination">
        <button 
          @click="loadPage(pagination.current_page - 1)" 
          :disabled="pagination.current_page <= 1"
          class="btn-page"
        >
          Trước
        </button>
        <span class="page-info">
          Trang {{ pagination.current_page }} / {{ pagination.last_page }}
          ({{ pagination.total }} bản ghi)
        </span>
        <button 
          @click="loadPage(pagination.current_page + 1)" 
          :disabled="pagination.current_page >= pagination.last_page"
          class="btn-page"
        >
          Sau
        </button>
      </div>
    </div>

    <!-- Error Message -->
    <div v-if="error" class="error-message">
      <p>{{ error }}</p>
    </div>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'Form01AReport',
  data() {
    return {
      filters: {
        ten_doituong: '',
        xa_phuong: '',
        quan_huyen: '',
        infrastructure_types: [],
        nam_tu: null,
        nam_den: null,
        user_name: '',
        user_phone: '',
        user_email: '',
        per_page: 20
      },
      reportData: [],
      summary: null,
      pagination: null,
      loading: false,
      error: null,
      currentYear: new Date().getFullYear()
    }
  },
  methods: {
    async generateReport(page = 1) {
      this.loading = true
      this.error = null
      
      try {
        const params = { ...this.filters, page }
        const response = await axios.get('/api/taisan/reports/form-01a', { params })
        
        this.reportData = response.data.data
        this.pagination = response.data.meta
      } catch (error) {
        this.error = 'Lỗi khi tạo báo cáo: ' + (error.response?.data?.message || error.message)
      } finally {
        this.loading = false
      }
    },

    async generateSummary() {
      this.loading = true
      this.error = null
      
      try {
        const response = await axios.get('/api/taisan/reports/form-01a/summary', { 
          params: this.filters 
        })
        
        this.summary = response.data.data
      } catch (error) {
        this.error = 'Lỗi khi tạo tổng hợp: ' + (error.response?.data?.message || error.message)
      } finally {
        this.loading = false
      }
    },

    async loadPage(page) {
      if (page >= 1 && page <= this.pagination.last_page) {
        await this.generateReport(page)
      }
    },

    resetFilters() {
      this.filters = {
        ten_doituong: '',
        xa_phuong: '',
        quan_huyen: '',
        infrastructure_types: [],
        nam_tu: null,
        nam_den: null,
        user_name: '',
        user_phone: '',
        user_email: '',
        per_page: 20
      }
      this.reportData = []
      this.summary = null
      this.pagination = null
      this.error = null
    },

    formatCurrency(value) {
      if (!value) return '0 VNĐ'
      
      const num = parseFloat(value)
      if (num >= 1000000000) {
        return (num / 1000000000).toFixed(2) + ' tỷ VNĐ'
      } else if (num >= 1000000) {
        return (num / 1000000).toFixed(2) + ' triệu VNĐ'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(2) + ' nghìn VNĐ'
      } else {
        return num.toLocaleString() + ' VNĐ'
      }
    }
  }
}
</script>

<style scoped>
.form-01a-report {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.report-header {
  text-align: center;
  margin-bottom: 30px;
}

.filter-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
}

.form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.form-group {
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 20px;
}

.btn-primary,
.btn-secondary,
.btn-outline,
.btn-page {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-outline {
  background: transparent;
  color: #007bff;
  border: 1px solid #007bff;
}

.summary-section {
  margin-bottom: 30px;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.summary-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
}

.summary-number {
  font-size: 2em;
  font-weight: bold;
  color: #007bff;
  margin: 10px 0;
}

.financial-summary {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table-container {
  overflow-x: auto;
}

.report-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.report-table th,
.report-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.report-table th {
  background: #f8f9fa;
  font-weight: 600;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-top: 20px;
}

.page-info {
  font-size: 14px;
  color: #666;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 15px;
  border-radius: 4px;
  margin-top: 20px;
}
</style>
