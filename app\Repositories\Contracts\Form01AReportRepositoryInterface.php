<?php

namespace App\Repositories\Contracts;

use Illuminate\Pagination\LengthAwarePaginator;

interface Form01AReportRepositoryInterface
{
    /**
     * Generate paginated Form 01A report data
     * 
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function generateReport(array $filters = [], int $perPage = 50): LengthAwarePaginator;

    /**
     * Generate Form 01A summary statistics
     * 
     * @param array $filters
     * @return array
     */
    public function generateSummary(array $filters = []): array;

    /**
     * Get infrastructure data from all tables (trambom, congdap, kenhmuong)
     * 
     * @param array $filters
     * @return \Illuminate\Database\Query\Builder
     */
    public function getInfrastructureData(array $filters = []);

    /**
     * Calculate GTCL (remaining value) using depreciation formula
     * 
     * @param int $currentYear
     * @param int $yearOfUse
     * @param float $originalCost
     * @return float
     */
    public function calculateGTCL(int $currentYear, int $yearOfUse, float $originalCost): float;
}
